import type { <PERSON>a, StoryObj } from "@storybook/react";

import LabHeaderMenu from "./LabHeaderMenu";

const meta: Meta<typeof LabHeaderMenu> = {
	title: "Components/LabHeaderMenu",
	component: LabHeaderMenu,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		title: {
			control: "text",
			description: "The title of the header menu",
		},
		description: {
			control: "text",
			description: "The description of the header menu",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		title: "RS Labbdata",
		description: "Tillgängliga provresultat från äldre journalsystem",
	},
};
