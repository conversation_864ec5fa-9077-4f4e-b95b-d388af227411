import { RsdsLogo } from "../rsds-logo/RsdsLogo";

export interface LabHeaderMenuProps {
	/**
	 * The title of the header menu.
	 */
	title: string;
	/**
	 * The description of the header menu.
	 */
	description: string;
}

export const LabHeaderMenu = ({ title, description }: LabHeaderMenuProps) => {
	return (
		<div className="flex w-full flex-col items-start border-b border-solid border-cp-grey-150 bg-cp-white h-36">
			<div className="h-36 self-stretch relative flex items-center px-12">
				<div className="absolute left-12 top-1/2 -translate-y-1/2">
					<RsdsLogo />
				</div>
				<div className="pl-36">
					<div className="flex flex-col justify-center items-start">
						<div className="text-cp-grey-800 font-sans text-base font-bold leading-6">
							{title}
						</div>
						<div className="text-cp-grey-800 font-sans text-base font-normal leading-6">
							{description}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default LabHeaderMenu;
