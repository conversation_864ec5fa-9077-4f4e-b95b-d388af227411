import LabHeaderMenu from "../../lab-header-menu/LabHeaderMenu";
import LabSearchInput from "../../lab-search-input/LabSearchInput";
import RsdsButton from "../../rsds-button/RsdsButton";
import RsdsLogo from "../../rsds-logo/RsdsLogo";

/**
 * Primary UI component for user interaction
 */
export const SearchPatient = () => {
	return (
		<div className="w-full">
			<LabHeaderMenu
				title="Labsvar i Skåne"
				description="Du är inloggad som <PERSON>, VoB Internmedicinsk vård Ystad "
			/>
			<div className="pl-[142px] border-b p-9 flex flex-col gap-y-3">
				<label htmlFor="pt">
					<div className="font-bold">Sök patient</div>
					<div>Ange personnummer med 12 siffror (ÅÅÅÅMMDD-XXXX)</div>
				</label>
				<LabSearchInput
					name="pt"
					placeholder=""
					value=""
					onChange={(value) => console.log("Search value:", value)}
					onSearch={(value) => console.log("Searching for:", value)}
				/>
			</div>
		</div>
	);
};

export default SearchPatient;
