import type { Meta, StoryObj } from "@storybook/react-vite";
import TempComponent from "./TempComponent";

const meta: Meta<typeof TempComponent> = {
	title: "Other/Temp Component",
	component: TempComponent,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		variant: {
			control: { type: "select" },
			options: ["primary", "secondary", "outline"],
		},
	},
};

export default meta;
type Story = StoryObj<typeof TempComponent>;

export const Primary: Story = {
	args: {
		variant: "primary",
		children: "text",
	},
};

export const Secondary: Story = {
	args: {
		variant: "secondary",
		children: "text",
	},
};

export const Outline: Story = {
	args: {
		variant: "outline",
		children: "text",
	},
};

export const Large: Story = {
	args: {
		children: "stor div",
		className: "text-lg",
	},
};
